#ifndef RTSP_SERVER_H
#define RTSP_SERVER_H

#include "common.h"
#include "video_encoder.h"
#include <memory>
#include <atomic>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <vector>
#include <map>
#include <queue>
#include <string>
#include <chrono>

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#else
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#endif

/**
 * @brief RTSP客户端连接信息
 */
struct RTSPClient {
    int socket = -1;                        // 客户端socket
    std::string address;                    // 客户端地址
    int port = 0;                          // 客户端端口
    std::string sessionId;                  // 会话ID
    std::chrono::steady_clock::time_point connectTime;  // 连接时间
    std::chrono::steady_clock::time_point lastActivity; // 最后活动时间
    
    // RTP参数
    int rtpPort = 0;                       // RTP端口
    int rtcpPort = 0;                      // RTCP端口
    uint32_t ssrc = 0;                     // 同步源标识符
    uint16_t sequenceNumber = 0;           // RTP序列号
    uint32_t timestamp = 0;                // RTP时间戳
    
    bool isActive = false;                 // 是否活跃
    std::atomic<uint64_t> sentPackets{0};  // 已发送包数
    std::atomic<uint64_t> sentBytes{0};    // 已发送字节数
};

/**
 * @brief RTSP服务器配置
 */
struct RTSPServerConfig {
    std::string address = "0.0.0.0";       // 服务器地址
    int port = 8554;                       // RTSP端口
    std::string streamPath = "/live";       // 流路径
    int maxClients = 10;                   // 最大客户端数
    int timeoutSeconds = 30;               // 客户端超时时间
    int bufferSize = 1024 * 1024;          // 发送缓冲区大小
    
    // RTP参数
    int rtpStartPort = 5004;               // RTP起始端口
    int rtpPortRange = 1000;               // RTP端口范围
    int mtu = 1400;                        // 最大传输单元
    
    // 服务器信息
    std::string serverName = "FaultDetectRTSP";
    std::string userAgent = "FaultDetectRTSP/1.0";
};

/**
 * @brief RTSP服务器状态
 */
enum class RTSPServerStatus {
    STOPPED,        // 已停止
    STARTING,       // 启动中
    RUNNING,        // 运行中
    STOPPING,       // 停止中
    ERROR          // 错误状态
};

/**
 * @brief RTSP消息类型
 */
enum class RTSPMethod {
    OPTIONS,
    DESCRIBE,
    SETUP,
    PLAY,
    PAUSE,
    TEARDOWN,
    GET_PARAMETER,
    SET_PARAMETER,
    UNKNOWN
};

/**
 * @brief RTSP请求结构
 */
struct RTSPRequest {
    RTSPMethod method = RTSPMethod::UNKNOWN;
    std::string uri;
    std::string version = "RTSP/1.0";
    std::map<std::string, std::string> headers;
    std::string body;
    int cseq = 0;
};

/**
 * @brief RTSP响应结构
 */
struct RTSPResponse {
    std::string version = "RTSP/1.0";
    int statusCode = 200;
    std::string statusText = "OK";
    std::map<std::string, std::string> headers;
    std::string body;
};

/**
 * @brief RTSP服务器
 * 
 * 实现RTSP协议的视频流推送服务，支持多客户端连接
 */
class RTSPServer {
public:
    RTSPServer();
    ~RTSPServer();
    
    // 禁用拷贝构造和赋值
    RTSPServer(const RTSPServer&) = delete;
    RTSPServer& operator=(const RTSPServer&) = delete;
    
    /**
     * @brief 初始化RTSP服务器
     * @param config 服务器配置
     * @return 是否初始化成功
     */
    bool initialize(const RTSPServerConfig& config);
    
    /**
     * @brief 启动RTSP服务器
     * @return 是否启动成功
     */
    bool start();
    
    /**
     * @brief 停止RTSP服务器
     */
    void stop();
    
    /**
     * @brief 广播编码数据包给所有客户端
     * @param packet 编码数据包
     * @return 是否广播成功
     */
    bool broadcastPacket(const EncodedPacket& packet);
    
    /**
     * @brief 获取服务器状态
     * @return 当前状态
     */
    RTSPServerStatus getStatus() const { return status_; }
    
    /**
     * @brief 获取当前客户端数量
     * @return 客户端数量
     */
    int getClientCount() const;
    
    /**
     * @brief 获取服务器配置
     * @return 当前配置
     */
    const RTSPServerConfig& getConfig() const { return config_; }
    
    /**
     * @brief 获取RTSP URL
     * @return RTSP访问URL
     */
    std::string getRTSPUrl() const;
    
    /**
     * @brief 检查是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized() const { return status_ != RTSPServerStatus::STOPPED; }
    
    /**
     * @brief 检查是否正在运行
     * @return 是否正在运行
     */
    bool isRunning() const { return status_ == RTSPServerStatus::RUNNING; }

private:
    // 配置和状态
    RTSPServerConfig config_;               // 服务器配置
    std::atomic<RTSPServerStatus> status_;  // 服务器状态
    std::atomic<bool> shouldStop_;          // 停止标志
    
    // 网络组件
    int serverSocket_ = -1;                 // 服务器socket
    std::vector<std::unique_ptr<RTSPClient>> clients_;  // 客户端列表
    mutable std::mutex clientsMutex_;       // 客户端列表互斥锁
    
    // 线程管理
    std::unique_ptr<std::thread> acceptThread_;     // 接受连接线程
    std::unique_ptr<std::thread> clientThread_;     // 客户端处理线程
    std::unique_ptr<std::thread> cleanupThread_;    // 清理线程
    
    // RTP相关
    std::atomic<int> nextRtpPort_;          // 下一个可用RTP端口
    std::map<int, bool> usedPorts_;         // 已使用端口映射
    mutable std::mutex portsMutex_;         // 端口互斥锁
    
    /**
     * @brief 接受客户端连接的工作线程
     */
    void acceptWorker();
    
    /**
     * @brief 处理客户端请求的工作线程
     */
    void clientWorker();
    
    /**
     * @brief 清理断开连接的工作线程
     */
    void cleanupWorker();
    
    /**
     * @brief 处理单个客户端连接
     * @param client 客户端连接
     */
    void handleClient(std::unique_ptr<RTSPClient> client);
    
    /**
     * @brief 解析RTSP请求
     * @param data 请求数据
     * @param request 解析后的请求
     * @return 是否解析成功
     */
    bool parseRTSPRequest(const std::string& data, RTSPRequest& request);
    
    /**
     * @brief 处理RTSP请求
     * @param client 客户端
     * @param request 请求
     * @param response 响应
     * @return 是否处理成功
     */
    bool handleRTSPRequest(RTSPClient* client, const RTSPRequest& request, RTSPResponse& response);
    
    /**
     * @brief 发送RTSP响应
     * @param client 客户端
     * @param response 响应
     * @return 是否发送成功
     */
    bool sendRTSPResponse(RTSPClient* client, const RTSPResponse& response);
    
    /**
     * @brief 发送RTP数据包
     * @param client 客户端
     * @param data 数据
     * @param size 数据大小
     * @param timestamp 时间戳
     * @param isKeyFrame 是否为关键帧
     * @return 是否发送成功
     */
    bool sendRTPPacket(RTSPClient* client, const uint8_t* data, size_t size, 
                       uint32_t timestamp, bool isKeyFrame);
    
    /**
     * @brief 分配RTP端口对
     * @param rtpPort RTP端口
     * @param rtcpPort RTCP端口
     * @return 是否分配成功
     */
    bool allocateRTPPorts(int& rtpPort, int& rtcpPort);
    
    /**
     * @brief 释放RTP端口对
     * @param rtpPort RTP端口
     * @param rtcpPort RTCP端口
     */
    void deallocateRTPPorts(int rtpPort, int rtcpPort);
    
    /**
     * @brief 生成会话ID
     * @return 会话ID字符串
     */
    std::string generateSessionId();
    
    /**
     * @brief 清理断开的客户端
     */
    void cleanupDisconnectedClients();
    
    /**
     * @brief 初始化网络
     * @return 是否初始化成功
     */
    bool initializeNetwork();
    
    /**
     * @brief 清理网络资源
     */
    void cleanupNetwork();
    
    /**
     * @brief 记录错误信息
     * @param message 错误消息
     */
    void logError(const std::string& message) const;
    
    /**
     * @brief 记录信息
     * @param message 信息消息
     */
    void logInfo(const std::string& message) const;
};

#endif // RTSP_SERVER_H
