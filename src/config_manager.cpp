#include "../include/common.h"
#include <fstream>
#include <iostream>
#include <sstream>

// 简单的JSON解析函数
bool parseJsonValue(const std::string& jsonStr, const std::string& key, std::string& value) {
    std::string searchKey = "\"" + key + "\"";
    size_t pos = jsonStr.find(searchKey);
    if (pos == std::string::npos) {
        return false;
    }
    
    // 找到键后，查找冒号
    pos = jsonStr.find(":", pos);
    if (pos == std::string::npos) {
        return false;
    }
    
    // 跳过冒号和空白
    pos++;
    while (pos < jsonStr.length() && (jsonStr[pos] == ' ' || jsonStr[pos] == '\t')) {
        pos++;
    }
    
    // 检查值类型
    if (pos >= jsonStr.length()) {
        return false;
    }
    
    if (jsonStr[pos] == '"') {
        // 字符串值
        size_t startPos = pos + 1;
        size_t endPos = jsonStr.find("\"", startPos);
        if (endPos == std::string::npos) {
            return false;
        }
        value = jsonStr.substr(startPos, endPos - startPos);
    } else if (jsonStr[pos] == '{' || jsonStr[pos] == '[') {
        // 对象或数组，不支持
        return false;
    } else {
        // 数字或布尔值
        size_t endPos = jsonStr.find_first_of(",}\n", pos);
        if (endPos == std::string::npos) {
            value = jsonStr.substr(pos);
        } else {
            value = jsonStr.substr(pos, endPos - pos);
        }
        // 去除尾部空白
        while (!value.empty() && (value.back() == ' ' || value.back() == '\t' || value.back() == '\r')) {
            value.pop_back();
        }
    }
    
    return true;
}

// 解析数字值
template<typename T>
bool parseJsonNumber(const std::string& jsonStr, const std::string& key, T& value) {
    std::string strValue;
    if (!parseJsonValue(jsonStr, key, strValue)) {
        return false;
    }
    
    try {
        if constexpr (std::is_same_v<T, int>) {
            value = std::stoi(strValue);
        } else if constexpr (std::is_same_v<T, double>) {
            value = std::stod(strValue);
        } else {
            return false;
        }
        return true;
    } catch (...) {
        return false;
    }
}

// 初始化Config命名空间中的变量
int Config::CAMERA_COUNT = 4;
int Config::CAMERA_WIDTH = 640;
int Config::CAMERA_HEIGHT = 480;
int Config::TARGET_FPS = 15;
double Config::MIN_CRACK_WIDTH = 0.1;
double Config::MIN_DAMAGE_SIZE = 10.0;
int Config::MAX_QUEUE_SIZE = 30;
int Config::PROCESS_TIMEOUT = 500;
int Config::RTSP_PORT = 8554;
std::string Config::RTSP_PATH = "/live";

// 初始化配置
bool Config::initializeConfig(const std::string& configPath) {
    return ConfigManager::getInstance().loadConfig(configPath);
}

// ConfigManager单例实现
ConfigManager& ConfigManager::getInstance() {
    static ConfigManager instance;
    return instance;
}

// 加载配置文件
bool ConfigManager::loadConfig(const std::string& configPath) {
    if (!parseJsonFile(configPath)) {
        std::cerr << "无法加载配置文件: " << configPath << std::endl;
        return false;
    }
    
    // 更新Config命名空间中的变量
    Config::CAMERA_COUNT = cameraCount_;
    Config::CAMERA_WIDTH = cameraWidth_;
    Config::CAMERA_HEIGHT = cameraHeight_;
    Config::TARGET_FPS = targetFPS_;
    Config::MIN_CRACK_WIDTH = minCrackWidth_;
    Config::MIN_DAMAGE_SIZE = minDamageSize_;
    Config::MAX_QUEUE_SIZE = maxQueueSize_;
    Config::PROCESS_TIMEOUT = processTimeout_;
    Config::RTSP_PORT = rtspPort_;
    Config::RTSP_PATH = rtspPath_;
    
    return true;
}

// 解析JSON文件
bool ConfigManager::parseJsonFile(const std::string& filePath) {
    std::ifstream file(filePath);
    if (!file.is_open()) {
        std::cerr << "无法打开配置文件: " << filePath << std::endl;
        return false;
    }
    
    std::stringstream buffer;
    buffer << file.rdbuf();
    std::string jsonStr = buffer.str();
    
    // 解析摄像头配置
    std::string cameraSection;
    size_t cameraStart = jsonStr.find("\"camera\"");
    if (cameraStart != std::string::npos) {
        cameraStart = jsonStr.find("{", cameraStart);
        if (cameraStart != std::string::npos) {
            size_t cameraEnd = jsonStr.find("}", cameraStart);
            if (cameraEnd != std::string::npos) {
                cameraSection = jsonStr.substr(cameraStart, cameraEnd - cameraStart + 1);
                
                parseJsonNumber(cameraSection, "count", cameraCount_);
                parseJsonNumber(cameraSection, "width", cameraWidth_);
                parseJsonNumber(cameraSection, "height", cameraHeight_);
                parseJsonNumber(cameraSection, "target_fps", targetFPS_);
                parseJsonNumber(cameraSection, "max_queue_size", maxQueueSize_);
            }
        }
    }
    
    // 解析检测配置
    std::string detectionSection;
    size_t detectionStart = jsonStr.find("\"detection\"");
    if (detectionStart != std::string::npos) {
        detectionStart = jsonStr.find("{", detectionStart);
        if (detectionStart != std::string::npos) {
            size_t detectionEnd = jsonStr.find("}", detectionStart);
            if (detectionEnd != std::string::npos) {
                detectionSection = jsonStr.substr(detectionStart, detectionEnd - detectionStart + 1);
                
                parseJsonNumber(detectionSection, "min_crack_width_mm", minCrackWidth_);
                parseJsonNumber(detectionSection, "min_damage_size_mm", minDamageSize_);
                parseJsonNumber(detectionSection, "process_timeout_ms", processTimeout_);
            }
        }
    }
    
    // 解析RTSP配置
    std::string rtspSection;
    size_t rtspStart = jsonStr.find("\"rtsp\"");
    if (rtspStart != std::string::npos) {
        rtspStart = jsonStr.find("{", rtspStart);
        if (rtspStart != std::string::npos) {
            size_t rtspEnd = jsonStr.find("}", rtspStart);
            if (rtspEnd != std::string::npos) {
                rtspSection = jsonStr.substr(rtspStart, rtspEnd - rtspStart + 1);
                
                parseJsonNumber(rtspSection, "port", rtspPort_);
                parseJsonValue(rtspSection, "path", rtspPath_);
            }
        }
    }
    
    return true;
}
